{"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "4.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": "jsdom/data-urls", "main": "lib/parser.js", "files": ["lib/"], "scripts": {"test": "jest", "coverage": "jest --coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"abab": "^2.0.6", "whatwg-mimetype": "^3.0.0", "whatwg-url": "^12.0.0"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "eslint": "^8.31.0", "jest": "^29.3.1", "minipass-fetch": "^3.0.1"}, "engines": {"node": ">=14"}, "jest": {"coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "coveragePathIgnorePatterns": ["<rootDir>/node_modules/(?!(abab/lib/atob.js))"]}}
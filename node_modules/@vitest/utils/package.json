{"name": "@vitest/utils", "type": "module", "version": "0.34.6", "description": "Shared Vitest utility functions", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./error": {"types": "./dist/error.d.ts", "import": "./dist/error.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./source-map": {"types": "./dist/source-map.d.ts", "import": "./dist/source-map.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "*.d.ts"], "dependencies": {"diff-sequences": "^29.4.3", "loupe": "^2.3.6", "pretty-format": "^29.5.0"}, "devDependencies": {"@jridgewell/trace-mapping": "^0.3.18"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}
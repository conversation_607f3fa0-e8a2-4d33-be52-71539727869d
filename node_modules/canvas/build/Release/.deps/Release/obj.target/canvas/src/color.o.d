cmd_Release/obj.target/canvas/src/color.o := c++ -o Release/obj.target/canvas/src/color.o ../src/color.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_FILE_OFFSET_BITS=64' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_JPEG' '-DHAVE_GIF' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/src -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.17.1/deps/v8/include -I../../nan -I/opt/homebrew/Cellar/cairo/1.18.4/include/cairo -I/opt/homebrew/Cellar/fontconfig/2.16.0/include -I/opt/homebrew/opt/freetype/include/freetype2 -I/opt/homebrew/opt/libpng/include/libpng16 -I/opt/homebrew/Cellar/libxext/1.3.6/include -I/opt/homebrew/Cellar/xorgproto/2024.1/include -I/opt/homebrew/Cellar/libxrender/0.9.12/include -I/opt/homebrew/Cellar/libx11/1.8.12/include -I/opt/homebrew/Cellar/libxcb/1.17.0/include -I/opt/homebrew/Cellar/libxau/1.0.12/include -I/opt/homebrew/Cellar/libxdmcp/1.1.5/include -I/opt/homebrew/Cellar/pixman/0.46.2/include/pixman-1 -I/opt/homebrew/Cellar/pango/1.56.4/include/pango-1.0 -I/opt/homebrew/Cellar/glib/2.84.3/include -I/opt/homebrew/Cellar/fribidi/1.0.16/include/fribidi -I/opt/homebrew/Cellar/harfbuzz/11.2.1/include/harfbuzz -I/opt/homebrew/Cellar/graphite2/1.3.14/include -I/opt/homebrew/Cellar/glib/2.84.3/include/glib-2.0 -I/opt/homebrew/Cellar/glib/2.84.3/lib/glib-2.0/include -I/opt/homebrew/opt/gettext/include -I/opt/homebrew/Cellar/pcre2/10.45/include -I/Library/Developer/CommandLineTools/SDKs/MacOSX15.sdk/usr/include/ffi -I/opt/homebrew/opt/jpeg-turbo/include -I/opt/homebrew/include  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/color.o.d.raw   -c
Release/obj.target/canvas/src/color.o: ../src/color.cc ../src/color.h
../src/color.cc:
../src/color.h:

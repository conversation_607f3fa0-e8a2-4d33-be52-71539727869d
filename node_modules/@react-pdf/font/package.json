{"name": "@react-pdf/font", "version": "4.0.2", "license": "MIT", "description": "Register font and emoji source for react-pdf document", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/diegomura/react-pdf#readme", "repository": {"type": "git", "url": "https://github.com/diegomura/react-pdf.git", "directory": "packages/font"}, "type": "module", "main": "./lib/index.js", "types": "./lib/index.d.ts", "browser": {"./lib/index.js": "./lib/index.browser.js"}, "scripts": {"test": "vitest", "build": "rimraf ./lib && rollup -c", "watch": "rimraf ./lib && rollup -c -w", "typecheck": "tsc --noEmit"}, "dependencies": {"@react-pdf/pdfkit": "^4.0.3", "@react-pdf/types": "^2.9.0", "fontkit": "^2.0.2", "is-url": "^1.2.4"}, "files": ["lib"], "devDependencies": {"@types/fontkit": "^2.0.7", "@types/is-url": "^1.2.32"}}
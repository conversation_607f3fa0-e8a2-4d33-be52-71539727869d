# Email, Innovars AI & Facturen Pagina Verbeteringen

## 📧 **DEEL 1: EMAIL PAGINA VERBETERINGEN**

### **1.1 Enhanced Email Interface**

#### **Email Categories & Smart Filtering:**
```tsx
interface EmailCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  count: number;
  filter: (email: Email) => boolean;
}

const emailCategories: EmailCategory[] = [
  {
    id: 'quotes',
    name: 'Offertes', 
    icon: <FileText className="w-4 h-4" />,
    color: 'bg-blue-500',
    count: 12,
    filter: (email) => email.subject.includes('offerte') || email.subject.includes('quote')
  },
  {
    id: 'invoices',
    name: '<PERSON>acturen',
    icon: <Receipt className="w-4 h-4" />,
    color: 'bg-green-500', 
    count: 8,
    filter: (email) => email.subject.includes('factuur') || email.subject.includes('invoice')
  },
  {
    id: 'customers',
    name: 'Klanten',
    icon: <Users className="w-4 h-4" />,
    color: 'bg-purple-500',
    count: 25,
    filter: (email) => email.category === 'customer_communication'
  },
  {
    id: 'suppliers',
    name: 'Leveranciers',
    icon: <Truck className="w-4 h-4" />,
    color: 'bg-orange-500',
    count: 6,
    filter: (email) => email.category === 'supplier'
  }
];
```

#### **Smart Email Actions:**
```tsx
// AI-powered email actions
const smartActions = [
  {
    label: 'Auto Quote Follow-up',
    description: 'Stuur automatische herinneringen voor openstaande offertes',
    icon: <Clock className="w-5 h-5" />,
    action: () => triggerAutoFollowup()
  },
  {
    label: 'Extract Invoice Data', 
    description: 'Haal automatisch factuurgegevens uit emails',
    icon: <Scan className="w-5 h-5" />,
    action: () => extractInvoiceData()
  },
  {
    label: 'Customer Sentiment Analysis',
    description: 'Analyseer klanttevredenheid in emails',
    icon: <TrendingUp className="w-5 h-5" />,
    action: () => analyzeSentiment()
  }
];
```

#### **Email Templates & Quick Responses:**
```tsx
const emailTemplates = [
  {
    name: 'Quote Follow-up',
    subject: 'Herinnering: Offerte #{quoteNumber}',
    body: `Beste {customerName},\n\nIk wilde even checken of u nog vragen heeft over de offerte voor {projectName}.\n\nVriendelijke groet,\n{userName}`
  },
  {
    name: 'Invoice Reminder',
    subject: 'Betalingsherinnering Factuur #{invoiceNumber}',
    body: `Beste {customerName},\n\nHierbij een vriendelijke herinnering voor factuur #{invoiceNumber}.\n\nBedrag: €{amount}\nVervaldatum: {dueDate}`
  },
  {
    name: 'Project Completion',
    subject: 'Project {projectName} voltooid',
    body: `Beste {customerName},\n\nHet project "{projectName}" is succesvol voltooid.\n\nFactuur volgt binnenkort.`
  }
];
```

### **1.2 Email Integration Features:**

#### **Outlook/Gmail Sync:**
```tsx
// Real-time email sync
const emailSync = {
  outlook: {
    clientId: process.env.VITE_OUTLOOK_CLIENT_ID,
    permissions: ['Mail.Read', 'Mail.Send', 'Mail.ReadWrite'],
    autoSync: true,
    syncInterval: 30000 // 30 seconds
  },
  gmail: {
    clientId: process.env.VITE_GMAIL_CLIENT_ID,
    permissions: ['gmail.readonly', 'gmail.send', 'gmail.modify'],
    autoSync: true,
    syncInterval: 30000
  }
};
```

#### **Smart Email Processing:**
```tsx
// AI email categorization  
interface ProcessedEmail {
  id: string;
  subject: string;
  sender: string;
  body: string;
  receivedAt: Date;
  category: 'quote_request' | 'invoice' | 'complaint' | 'general';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  extractedData?: {
    customerName?: string;
    projectType?: string;
    budget?: number;
    deadline?: Date;
  };
  suggestedActions: Array<{
    type: 'create_quote' | 'follow_up' | 'schedule_call';
    description: string;
  }>;
}
```

---

## 🤖 **DEEL 2: INNOVARS AI PAGINA VERBETERINGEN**

### **2.1 Enhanced AI Interface**

#### **Conversational Memory & Context:**
```tsx
interface AIConversation {
  id: string;
  title: string;
  messages: AIMessage[];
  context: {
    currentProject?: string;
    activeCustomer?: string;
    recentQuotes?: string[];
    marketData?: any;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Context-aware responses
const contextualPrompts = [
  "Gebaseerd op je recente quote voor {customerName}, zou je ook {suggestedService} kunnen aanbieden",
  "De marktprijzen voor {materialType} zijn deze week {percentageChange}% gestegen",
  "Je hebt 3 openstaande offertes die binnen 48 uur verlopen"
];
```

#### **Smart Suggestion Categories:**
```tsx
const aiSuggestionCategories = {
  projectTypes: [
    {
      name: 'Badkamer Renovatie',
      averagePrice: '€12,500',
      duration: '2-3 weken',
      materials: ['Tegels', 'Sanitair', 'Leidingwerk'],
      prompt: 'Maak een offerte voor complete badkamerrenovatie inclusief tegelwerk en sanitair'
    },
    {
      name: 'Keuken Verbouwing', 
      averagePrice: '€18,500',
      duration: '3-4 weken',
      materials: ['Keukenkastjes', 'Werkblad', 'Apparatuur'],
      prompt: 'Bereken kosten voor keukenverbouwing met nieuwe kastjes en apparatuur'
    },
    {
      name: 'Dakisolatie',
      averagePrice: '€4,200',
      duration: '2-5 dagen', 
      materials: ['Isolatiemateriaal', 'Dampscherm', 'Afwerking'],
      prompt: 'Offerte voor dakisolatie inclusief materiaal en arbeid'
    }
  ],
  
  marketInsights: [
    {
      category: 'Prijstrends',
      insights: [
        'Bouwmaterialen zijn 8% gestegen deze maand',
        'Vraag naar zonnepanelen stijgt met 15%',
        'Arbeidskosten zijn stabiel gebleven'
      ]
    },
    {
      category: 'Seizoensadvies',
      insights: [
        'Winter is ideaal voor binnen renovaties',
        'Voorjaar: hoge vraag naar tuin/terras projecten',
        'Zomer: focus op dakwerkzaamheden'
      ]
    }
  ]
};
```

#### **Voice Commands & Advanced Input:**
```tsx
const voiceCommands = [
  {
    command: "Maak nieuwe offerte voor {customerName}",
    action: (params) => createQuote(params.customerName),
    category: 'quote_management'
  },
  {
    command: "Toon me de winst van deze maand", 
    action: () => showMonthlyProfit(),
    category: 'analytics'
  },
  {
    command: "Stuur herinnering naar {customerName}",
    action: (params) => sendReminder(params.customerName),
    category: 'communication'
  },
  {
    command: "Wat zijn de huidige bouwprijzen voor {material}",
    action: (params) => getMarketPrices(params.material),
    category: 'market_data'
  }
];
```

### **2.2 AI-Powered Features:**

#### **Smart Quote Generation:**
```tsx
// AI analyzes voice/text input and generates structured quote
const aiQuoteGeneration = {
  inputAnalysis: {
    extractEntities: ['customerName', 'projectType', 'budget', 'timeline'],
    inferMaterials: true,
    suggestAddOns: true,
    marketPriceCheck: true
  },
  
  outputGeneration: {
    itemizedList: true,
    laborCalculation: true,
    materialCosts: true,
    profitMargin: 'auto', // AI optimizes based on market data
    timeline: 'estimated'
  },
  
  qualityChecks: [
    'Price reasonableness vs market average',
    'Material compatibility check', 
    'Timeline feasibility',
    'Profit margin validation'
  ]
};
```

#### **Predictive Analytics:**
```tsx
const predictiveFeatures = {
  customerBehavior: {
    likelyToAccept: 'probability_score',
    bestTimeToFollowUp: 'datetime_prediction',
    priceElasticity: 'price_sensitivity_score'
  },
  
  marketForecasting: {
    materialPriceTrends: '3_month_forecast',
    demandPrediction: 'seasonal_demand_curve',
    competitorAnalysis: 'market_positioning'
  },
  
  businessOptimization: {
    optimalPricing: 'price_recommendation',
    resourcePlanning: 'capacity_optimization',
    cashflowForecast: 'financial_projection'
  }
};
```

---

## 🧾 **DEEL 3: NIEUWE FACTUREN PAGINA**

### **3.1 Complete Invoice Management System**

#### **Component Structure:**
```tsx
// Main Invoices Page
src/pages/Invoices.tsx
src/components/invoices/
├── InvoiceList.tsx          // Overview van alle facturen
├── InvoiceForm.tsx          // Nieuwe factuur maken
├── InvoiceView.tsx          // Factuur details bekijken
├── InvoiceCategories.tsx    // Aankoop vs Verkoop tabs
├── BookkeeperExport.tsx     // Export naar boekhouder
├── InvoiceAnalytics.tsx     // Financiële analytics
└── InvoiceTemplates.tsx     // Factuur templates
```

#### **Invoice Data Structure:**
```tsx
interface Invoice {
  id: string;
  invoiceNumber: string;
  type: 'sales' | 'purchase'; // Verkoop of Aankoop
  
  // Basic Info
  issueDate: Date;
  dueDate: Date;
  paymentDate?: Date;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  
  // Customer/Supplier Info  
  contact: {
    id: string;
    name: string;
    email: string;
    address: string;
    vatNumber?: string;
    kvkNumber?: string;
  };
  
  // Financial Details
  items: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    vatRate: number; // 0, 9, 21
    total: number;
  }>;
  
  subtotal: number;
  vatAmount: number;
  totalAmount: number;
  
  // Metadata
  relatedQuoteId?: string;
  projectName?: string;
  notes?: string;
  attachments?: string[];
  
  // Bookkeeping
  bookkeepingExported: boolean;
  bookkeepingDate?: Date;
  bookkeepingReference?: string;
  accountingCategory: string;
}
```

### **3.2 Verkoop Facturen (Sales Invoices)**

#### **Features:**
```tsx
const salesInvoiceFeatures = {
  creation: {
    fromQuote: 'Convert accepted quote to invoice',
    fromScratch: 'Manual invoice creation',
    recurring: 'Set up recurring invoices',
    templates: 'Use predefined templates'
  },
  
  management: {
    tracking: 'Payment status tracking',
    reminders: 'Automatic payment reminders',
    overdue: 'Overdue invoice management',
    partialPayments: 'Handle partial payments'
  },
  
  automation: {
    emailSending: 'Auto-send invoices to customers',
    followUp: 'Smart follow-up sequences',
    lateNotices: 'Automated late payment notices',
    thankYou: 'Payment confirmation emails'
  }
};
```

#### **Sales Invoice List View:**
```tsx
const SalesInvoiceList = () => {
  const statusFilters = [
    { label: 'Alle', value: 'all', count: 45 },
    { label: 'Concept', value: 'draft', count: 3 },
    { label: 'Verzonden', value: 'sent', count: 12 },
    { label: 'Betaald', value: 'paid', count: 28 },
    { label: 'Te laat', value: 'overdue', count: 2 }
  ];

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatCard title="Totaal Uitstaand" value="€24,500" color="blue" />
        <StatCard title="Deze Maand" value="€18,200" color="green" />
        <StatCard title="Te Laat" value="€2,300" color="red" />
        <StatCard title="Gem. Betaaltijd" value="18 dagen" color="purple" />
      </div>
      
      {/* Invoice Table */}
      <InvoiceTable 
        invoices={salesInvoices}
        type="sales"
        onView={handleViewInvoice}
        onEdit={handleEditInvoice}
        onSend={handleSendInvoice}
        onMarkPaid={handleMarkPaid}
      />
    </div>
  );
};
```

### **3.3 Aankoop Facturen (Purchase Invoices)**

#### **Features:**
```tsx
const purchaseInvoiceFeatures = {
  recording: {
    supplier: 'Supplier invoice recording',
    scanning: 'OCR scan for automatic data entry',
    categorization: 'Expense category assignment',
    approval: 'Approval workflow for large expenses'
  },
  
  tracking: {
    paymentDue: 'Track payment due dates',
    cashFlow: 'Cash flow impact analysis',
    budgetMonitoring: 'Project budget tracking',
    supplierPerformance: 'Supplier payment terms tracking'
  },
  
  integration: {
    bankConnection: 'Connect to bank for payment matching',
    expenseCategories: 'Automatic expense categorization',
    taxDeduction: 'VAT reclaim tracking',
    projectCosts: 'Assign costs to specific projects'
  }
};
```

#### **Purchase Invoice Form:**
```tsx
const PurchaseInvoiceForm = () => {
  const expenseCategories = [
    'Materialen - Bouw',
    'Materialen - Sanitair', 
    'Materialen - Elektra',
    'Gereedschap',
    'Transport/Brandstof',
    'Kantoorkosten',
    'Marketing',
    'Verzekeringen',
    'Onderhoud/Reparatie'
  ];

  return (
    <form className="space-y-6">
      {/* Supplier Selection */}
      <SupplierSelect 
        suppliers={suppliers}
        onSelect={handleSupplierSelect}
        allowNewSupplier={true}
      />
      
      {/* Invoice Details */}
      <InvoiceDetailsSection 
        includeOCRScanning={true}
        expenseCategories={expenseCategories}
      />
      
      {/* Project Assignment */}
      <ProjectAssignment 
        projects={activeProjects}
        allowSplit={true} // Split costs across multiple projects
      />
      
      {/* VAT & Tax Info */}
      <VATSection 
        defaultRate={21}
        allowZeroVAT={true}
        trackDeductible={true}
      />
    </form>
  );
};
```

### **3.4 Boekhouder Export Functionaliteit**

#### **Export Formats:**
```tsx
const bookkeepingExports = {
  formats: [
    {
      name: 'Excel (.xlsx)',
      description: 'Standaard Excel format voor meeste boekhouders',
      fields: ['Datum', 'Factuurnummer', 'Klant/Leverancier', 'Bedrag ex BTW', 'BTW', 'Totaal', 'Categorie'],
      filename: 'facturen_export_{startDate}_{endDate}.xlsx'
    },
    {
      name: 'CSV',
      description: 'Komma-gescheiden bestand voor import in boekhoudpakketten',
      fields: 'same_as_excel',
      filename: 'facturen_export_{startDate}_{endDate}.csv'
    },
    {
      name: 'Exact Online',
      description: 'Direct import format voor Exact Online',
      fields: 'exact_online_mapping',
      filename: 'exact_import_{startDate}_{endDate}.xml'
    },
    {
      name: 'Twinfield',
      description: 'Import format voor Twinfield boekhoudpakket',
      fields: 'twinfield_mapping', 
      filename: 'twinfield_import_{startDate}_{endDate}.xml'
    }
  ],
  
  filters: {
    dateRange: 'Start- en einddatum selectie',
    invoiceType: 'Verkoop, Aankoop, of Beide',
    paymentStatus: 'Alleen betaalde facturen of alle',
    categories: 'Specifieke uitgavencategorieën',
    projects: 'Per project exporteren'
  }
};
```

#### **Bookkeeper Export Component:**
```tsx
const BookkeeperExport = () => {
  const [exportConfig, setExportConfig] = useState({
    format: 'excel',
    dateRange: { start: '', end: '' },
    includeTypes: ['sales', 'purchase'],
    onlyPaid: false,
    groupByMonth: false
  });

  const handleExport = async () => {
    const data = await generateExportData(exportConfig);
    
    switch(exportConfig.format) {
      case 'excel':
        exportToExcel(data, exportConfig);
        break;
      case 'csv':
        exportToCSV(data, exportConfig);
        break;
      case 'exact':
        exportToExactOnline(data, exportConfig);
        break;
    }
    
    // Track export voor boekhouder
    await trackExport({
      exportDate: new Date(),
      format: exportConfig.format,
      recordCount: data.length,
      userId: currentUser.id
    });
  };

  return (
    <div className="bg-slate-800 rounded-xl p-6">
      <h3 className="text-xl font-bold text-white mb-4">
        Export naar Boekhouder
      </h3>
      
      {/* Export Configuration */}
      <ExportConfigForm 
        config={exportConfig}
        onChange={setExportConfig}
      />
      
      {/* Export Summary */}
      <ExportSummary 
        invoiceCount={filteredInvoices.length}
        totalAmount={totalAmount}
        vatAmount={vatAmount}
      />
      
      {/* Export Button */}
      <button 
        onClick={handleExport}
        className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg"
      >
        <Download className="w-5 h-5 mr-2" />
        Exporteren voor Boekhouder
      </button>
    </div>
  );
};
```

### **3.5 Financial Analytics & Reporting**

#### **Dashboard Widgets:**
```tsx
const invoiceAnalytics = {
  cashFlow: {
    incomingPayments: 'Verwachte betalingen komende 30 dagen',
    outgoingPayments: 'Te betalen facturen komende 30 dagen', 
    netCashFlow: 'Netto cashflow projectie',
    overdueTrends: 'Trend in te late betalingen'
  },
  
  profitability: {
    grossProfit: 'Bruto winst per periode',
    profitMargins: 'Winstmarges per projecttype',
    expenseBreakdown: 'Uitgaven per categorie',
    yearOverYear: 'Jaar-op-jaar vergelijking'
  },
  
  operationalMetrics: {
    averagePaymentTime: 'Gemiddelde betaaltijd klanten',
    invoiceVolume: 'Aantal facturen per maand',
    disputeRate: 'Percentage betwiste facturen',
    automationSavings: 'Tijdsbesparing door automatisering'
  }
};
```

### **3.6 Smart Features & Automation**

#### **AI-Powered Invoice Processing:**
```tsx
const aiInvoiceFeatures = {
  ocrScanning: {
    description: 'Scan supplier invoices and extract data automatically',
    accuracy: '95%+',
    supportedFormats: ['PDF', 'JPG', 'PNG'],
    extractedFields: ['Amount', 'Date', 'Supplier', 'VAT', 'Items']
  },
  
  duplicateDetection: {
    description: 'Automatically detect duplicate invoices',
    checkFields: ['Amount', 'Supplier', 'Date Range'],
    warningThreshold: '90% similarity'
  },
  
  expenseClassification: {
    description: 'AI categorizes expenses automatically',
    accuracy: '88%+',
    learningEnabled: true,
    categories: 'User-defined + standard categories'
  },
  
  fraudDetection: {
    description: 'Detect suspicious invoices and transactions',
    checks: ['Unusual amounts', 'New suppliers', 'Pattern anomalies'],
    riskScoring: 'Low/Medium/High risk classification'
  }
};
```

---

## 🎯 **IMPLEMENTATIE ROADMAP**

### **Week 1: Email Verbeteringen**
- [ ] Smart email categorization
- [ ] Email templates system
- [ ] Outlook/Gmail integration
- [ ] Auto-follow up system

### **Week 2: AI Enhancements** 
- [ ] Conversational memory
- [ ] Voice commands
- [ ] Smart quote generation
- [ ] Market insights integration

### **Week 3: Invoice System Foundation**
- [ ] Invoice data models
- [ ] Sales invoice CRUD
- [ ] Purchase invoice CRUD
- [ ] Basic export functionality

### **Week 4: Advanced Invoice Features**
- [ ] OCR scanning for purchase invoices
- [ ] Automated payment reminders
- [ ] Financial analytics dashboard
- [ ] Bookkeeper export formats

### **Week 5: Integration & Polish**
- [ ] Email-Invoice integration
- [ ] AI-Invoice integration  
- [ ] Advanced reporting
- [ ] User testing & refinement

---

**Met deze implementatie krijg je een complete business management suite die alle aspecten van je bedrijf efficiënt beheert! 🚀**

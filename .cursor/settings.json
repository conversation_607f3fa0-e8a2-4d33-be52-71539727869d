{"turboConsoleLog.insertEnclosingClass": true, "turboConsoleLog.insertEnclosingFunction": true, "turboConsoleLog.wrapLogMessage": true, "turboConsoleLog.logMessagePrefix": "🔍 DEBUG:", "turboConsoleLog.addSemicolonInTheEnd": true, "turboConsoleLog.insertExtraNewLineBeforeLogStatement": true, "turboConsoleLog.insertExtraNewLineAfterLogStatement": false, "turboConsoleLog.quoteMark": "`", "turboConsoleLog.logType": "console.log", "turboConsoleLog.logFunction": "console.log", "turboConsoleLog.delimiterInsideMessage": " | ", "turboConsoleLog.includeFileNameAndLineNum": true, "turboConsoleLog.includeFilePath": false, "turboConsoleLog.logMessageSuffix": "", "turboConsoleLog.logMessageDelimiter": " | ", "turboConsoleLog.logMessageQuoteMark": "`", "turboConsoleLog.logMessageSemicolon": true, "turboConsoleLog.logMessageExtraNewLineBefore": true, "turboConsoleLog.logMessageExtraNewLineAfter": false, "turboConsoleLog.logMessageIncludeFileNameAndLineNum": true, "turboConsoleLog.logMessageIncludeFilePath": false, "turboConsoleLog.logMessageIncludeEnclosingClass": true, "turboConsoleLog.logMessageIncludeEnclosingFunction": true, "turboConsoleLog.logMessageWrapLogMessage": true, "turboConsoleLog.logMessageDelimiterInsideMessage": " | ", "turboConsoleLog.logMessageLogType": "console.log", "turboConsoleLog.logMessageLogFunction": "console.log"}
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Image configuration
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
        port: '3001',
        pathname: '/**',
      },
    ],
    formats: ['image/webp', 'image/avif'],
  },

  // AI Model configuration
  experimental: {
    // Enable AI features
    serverComponentsExternalPackages: ['@anthropic-ai/sdk', 'openai'],
    
    // Enable streaming responses
    serverActions: {
      bodySizeLimit: '2mb',
    },
  },

  // Environment variables for AI models
  env: {
    // OpenAI Configuration
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    OPENAI_MODEL: process.env.OPENAI_MODEL || 'gpt-4',
    
    // Anthropic Configuration
    ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY,
    ANTHROPIC_MODEL: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
    
    // Local AI Models (LM Studio)
    LOCAL_AI_URL: process.env.LOCAL_AI_URL || 'http://localhost:1234',
    LOCAL_AI_MODELS: process.env.LOCAL_AI_MODELS || 'qwen2.5-coder-32b,deepseek-coder-6.7b,llama-2-13b-chat,mistral-7b-instruct,wizardlm-2-7b',
    
    // Voice Processing
    WHISPER_MODEL: process.env.WHISPER_MODEL || 'whisper-1',
    
    // WhatsApp Integration
    WHATSAPP_API_TOKEN: process.env.WHATSAPP_API_TOKEN,
    WHATSAPP_PHONE_NUMBER_ID: process.env.WHATSAPP_PHONE_NUMBER_ID,
    
    // PDF Generation
    PDF_API_KEY: process.env.PDF_API_KEY,
    
    // Multi-language support
    DEFAULT_LANGUAGE: process.env.DEFAULT_LANGUAGE || 'fr',
    SUPPORTED_LANGUAGES: process.env.SUPPORTED_LANGUAGES || 'fr,nl,en',
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
        ],
      },
      // CORS headers for AI API
      {
        source: '/api/ai/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ]
  },

  // Webpack configuration for AI models
  webpack: (config, { isServer }) => {
    // Handle AI model files
    config.module.rules.push({
      test: /\.(bin|gguf|safetensors)$/,
      type: 'asset/resource',
    });

    // Optimize for AI processing
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    return config;
  },

  // API routes configuration
  async rewrites() {
    return [
      // Proxy AI requests to local models
      {
        source: '/api/ai/local/:path*',
        destination: 'http://localhost:1234/v1/:path*',
      },
      // Proxy OpenAI requests
      {
        source: '/api/ai/openai/:path*',
        destination: 'https://api.openai.com/v1/:path*',
      },
      // Proxy Anthropic requests
      {
        source: '/api/ai/anthropic/:path*',
        destination: 'https://api.anthropic.com/v1/:path*',
      },
    ];
  },

  // Performance optimizations
  compress: true,
  poweredByHeader: false,
  
  // PWA support for mobile app
  async redirects() {
    return [
      {
        source: '/app',
        destination: '/dashboard',
        permanent: true,
      },
    ];
  },
}

module.exports = nextConfig 
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
ANTHROPIC_API_KEY=your_anthropic_api_key
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Local AI Models (LM Studio)
LOCAL_AI_URL=http://localhost:1234
LOCAL_AI_MODELS=qwen2.5-coder-32b,deepseek-coder-6.7b,llama-2-13b-chat,mistral-7b-instruct,wizardlm-2-7b

# Voice Processing
WHISPER_MODEL=whisper-1
WHISPER_API_KEY=your_openai_api_key

# WhatsApp Integration
WHATSAPP_API_TOKEN=your_whatsapp_business_api_token
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id
WHATSAPP_VERIFY_TOKEN=your_whatsapp_verify_token

# PDF Generation
PDF_API_KEY=your_pdf_api_key
PDF_SERVICE_URL=https://api.pdfgenerator.com

# Multi-language Support
DEFAULT_LANGUAGE=fr
SUPPORTED_LANGUAGES=fr,nl,en

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_APP_NAME=Renalto
NEXT_PUBLIC_APP_VERSION=1.0.0

# Development
NODE_ENV=development
DEBUG=renalto:*

# Security
JWT_SECRET=your_jwt_secret_key
ENCRYPTION_KEY=your_encryption_key

# Database (if using separate database)
DATABASE_URL=your_database_url

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# File Storage
STORAGE_BUCKET=renalto-storage
STORAGE_REGION=eu-west-1

# Analytics
ANALYTICS_ID=your_analytics_id
SENTRY_DSN=your_sentry_dsn

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_VOICE_RECOGNITION=true
ENABLE_WHATSAPP_INTEGRATION=true
ENABLE_PDF_GENERATION=true
ENABLE_MULTI_LANGUAGE=true 
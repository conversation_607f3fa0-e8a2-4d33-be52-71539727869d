import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { mockQuotes, mockCustomers, mockProjects } from './mockData';

// Types
export interface Quote {
  id: string;
  number: string;
  customerId: string;
  customerName: string;
  projectName: string;
  description: string;
  items: QuoteItem[];
  totalAmount: number;
  taxRate: number;
  validUntil: Date;
  createdAt: Date;
  status: 'draft' | 'sent' | 'accepted' | 'rejected' | 'expired';
}

export interface QuoteItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  unit: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  company?: string;
  address: string;
  createdAt: Date;
  status: 'active' | 'inactive' | 'prospect' | 'lead';
}

export interface Project {
  id: string;
  name: string;
  customerId: string;
  customerName: string;
  description: string;
  location: string;
  status: 'planning' | 'active' | 'completed' | 'on-hold' | 'cancelled';
  startDate: Date;
  endDate?: Date;
  budget: number;
  actualCost: number;
  progress: number; // 0-100
  photos: string[];
  notes: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user';
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  language: 'nl' | 'en' | 'fr';
  notifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  currency: 'EUR' | 'USD';
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  timeFormat: '12h' | '24h';
  autoSave: boolean;
  defaultQuoteTemplate: string;
  defaultProjectStatus: 'planning' | 'active';
}

export interface CompanySettings {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  kvkNumber: string;
  btwNumber: string;
  logo?: string;
  website?: string;
  description?: string;
}

export interface NotificationSettings {
  id: string;
  type: 'email' | 'sms' | 'push';
  event: 'quote_created' | 'quote_accepted' | 'quote_rejected' | 'project_started' | 'project_completed';
  enabled: boolean;
  recipients: string[];
}

export interface AppState {
  // Data
  quotes: Quote[];
  customers: Customer[];
  projects: Project[];
  user: User | null;
  companySettings: CompanySettings | null;
  notificationSettings: NotificationSettings[];
  
  // UI State
  isLoading: boolean;
  error: string | null;
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  
  // Actions
  setQuotes: (quotes: Quote[]) => void;
  addQuote: (quote: Quote) => void;
  updateQuote: (id: string, updates: Partial<Quote>) => void;
  deleteQuote: (id: string) => void;
  
  setCustomers: (customers: Customer[]) => void;
  addCustomer: (customer: Customer) => void;
  updateCustomer: (id: string, updates: Partial<Customer>) => void;
  deleteCustomer: (id: string) => void;
  
  setProjects: (projects: Project[]) => void;
  addProject: (project: Project) => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  
  setUser: (user: User) => void;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;
  
  setCompanySettings: (settings: CompanySettings) => void;
  updateCompanySettings: (updates: Partial<CompanySettings>) => void;
  
  setNotificationSettings: (settings: NotificationSettings[]) => void;
  updateNotificationSetting: (id: string, updates: Partial<NotificationSettings>) => void;
  
        setLoading: (loading: boolean) => void;
      setError: (error: string | null) => void;
      setSidebarOpen: (open: boolean) => void;
      setSidebarCollapsed: (collapsed: boolean) => void;
  
  // Computed values
  getQuoteById: (id: string) => Quote | undefined;
  getCustomerById: (id: string) => Customer | undefined;
  getProjectById: (id: string) => Project | undefined;
  getQuotesByStatus: (status: Quote['status']) => Quote[];
  getQuotesByCustomer: (customerId: string) => Quote[];
  getProjectsByStatus: (status: Project['status']) => Project[];
  getProjectsByCustomer: (customerId: string) => Project[];
  showToast: (message: string, type?: 'success' | 'error' | 'info') => void;
}

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // Initial state
      quotes: mockQuotes,
      customers: mockCustomers,
      projects: mockProjects,
      user: null,
      companySettings: null,
      notificationSettings: [],
      isLoading: false,
      error: null,
      sidebarOpen: false,
      sidebarCollapsed: typeof window !== 'undefined' 
        ? JSON.parse(localStorage.getItem('sidebarCollapsed') || 'false')
        : false,
      
      // Actions
      setQuotes: (quotes) => set({ quotes }),
      addQuote: (quote) => set((state) => ({ 
        quotes: [...state.quotes, quote] 
      })),
      updateQuote: (id, updates) => set((state) => ({
        quotes: state.quotes.map(quote => 
          quote.id === id ? { ...quote, ...updates } : quote
        )
      })),
      deleteQuote: (id) => set((state) => ({
        quotes: state.quotes.filter(quote => quote.id !== id)
      })),
      
      setCustomers: (customers) => set({ customers }),
      addCustomer: (customer) => set((state) => ({ 
        customers: [...state.customers, customer] 
      })),
      updateCustomer: (id, updates) => set((state) => ({
        customers: state.customers.map(customer => 
          customer.id === id ? { ...customer, ...updates } : customer
        )
      })),
      deleteCustomer: (id) => set((state) => ({
        customers: state.customers.filter(customer => customer.id !== id)
      })),
      
      setProjects: (projects) => set({ projects }),
      addProject: (project) => set((state) => ({ 
        projects: [...state.projects, project] 
      })),
      updateProject: (id, updates) => set((state) => ({
        projects: state.projects.map(project => 
          project.id === id ? { ...project, ...updates } : project
        )
      })),
      deleteProject: (id) => set((state) => ({
        projects: state.projects.filter(project => project.id !== id)
      })),
      
      setUser: (user) => set({ user }),
      updateUserPreferences: (preferences) => set((state) => ({
        user: state.user ? {
          ...state.user,
          preferences: { ...state.user.preferences, ...preferences }
        } : null
      })),
      
      setCompanySettings: (companySettings) => set({ companySettings }),
      updateCompanySettings: (updates) => set((state) => ({
        companySettings: state.companySettings ? {
          ...state.companySettings,
          ...updates
        } : null
      })),
      
      setNotificationSettings: (notificationSettings) => set({ notificationSettings }),
      updateNotificationSetting: (id, updates) => set((state) => ({
        notificationSettings: state.notificationSettings.map(setting => 
          setting.id === id ? { ...setting, ...updates } : setting
        )
      })),
      
      setLoading: (isLoading) => set({ isLoading }),
      setError: (error) => set({ error }),
      setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),
      setSidebarCollapsed: (sidebarCollapsed) => {
        if (typeof window !== 'undefined') {
          localStorage.setItem('sidebarCollapsed', JSON.stringify(sidebarCollapsed));
        }
        set({ sidebarCollapsed });
      },
      
      // Computed values
      getQuoteById: (id) => get().quotes.find(quote => quote.id === id),
      getCustomerById: (id) => get().customers.find(customer => customer.id === id),
      getProjectById: (id) => get().projects.find(project => project.id === id),
      getQuotesByStatus: (status) => get().quotes.filter(quote => quote.status === status),
      getQuotesByCustomer: (customerId) => get().quotes.filter(quote => quote.customerId === customerId),
      getProjectsByStatus: (status) => get().projects.filter(project => project.status === status),
      getProjectsByCustomer: (customerId) => get().projects.filter(project => project.customerId === customerId),
      showToast: (message: string, type = 'info') => {
        // Simple console log for now, can be enhanced later
        console.log(`Toast [${type}]: ${message}`);
      },
    }),
    {
      name: 'app-store',
    }
  )
);

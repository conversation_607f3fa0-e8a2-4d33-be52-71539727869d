import React, { useState, useEffect } from 'react';
import { 
  X, 
  Plus, 
  Trash2, 
  FileText, 
  Sparkles, 
  Calendar,
  User,
  Euro,
  Download,
  Eye,
  Check,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Select } from '@/components/ui/Select';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { invoiceApi } from '@/api/invoices';
import { AIInvoiceRequest, Invoice } from '@/types/invoice';
import { cn } from '@/utils/cn';

interface NewAIInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvoiceCreated: (invoice: Invoice) => void;
}

type TabType = 'quote' | 'manual';

interface ManualItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
}

export const NewAIInvoiceModal: React.FC<NewAIInvoiceModalProps> = ({
  isOpen,
  onClose,
  onInvoiceCreated
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('quote');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form data
  const [selectedQuoteId, setSelectedQuoteId] = useState<string>('');
  const [customerId, setCustomerId] = useState<string>('');
  const [projectName, setProjectName] = useState<string>('');
  const [invoiceDate, setInvoiceDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [dueDate, setDueDate] = useState<string>(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);
  const [vatPercentage, setVatPercentage] = useState<number>(21);
  
  // Manual items
  const [manualItems, setManualItems] = useState<ManualItem[]>([]);
  
  // Data
  const [approvedQuotes, setApprovedQuotes] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);

  // Load data on mount
  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  const loadData = async () => {
    try {
      const [quotes, customersData] = await Promise.all([
        invoiceApi.getApprovedQuotes(),
        // Mock customers data
        Promise.resolve([
          { id: '1', name: 'Jan Vermeulen', email: '<EMAIL>' },
          { id: '2', name: 'Marie Dubois', email: '<EMAIL>' },
          { id: '3', name: 'Piet Bakker', email: '<EMAIL>' }
        ])
      ]);
      
      setApprovedQuotes(quotes);
      setCustomers(customersData);
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Fout bij het laden van gegevens');
    }
  };

  const addManualItem = () => {
    const newItem: ManualItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      unitPrice: 0
    };
    setManualItems([...manualItems, newItem]);
  };

  const updateManualItem = (id: string, field: keyof ManualItem, value: string | number) => {
    setManualItems(prev => 
      prev.map(item => 
        item.id === id 
          ? { ...item, [field]: value }
          : item
      )
    );
  };

  const removeManualItem = (id: string) => {
    setManualItems(prev => prev.filter(item => item.id !== id));
  };

  const handleQuoteChange = (quoteId: string) => {
    setSelectedQuoteId(quoteId);
    const quote = approvedQuotes.find(q => q.id === quoteId);
    if (quote) {
      setCustomerId(quote.customerId || '');
      setProjectName(quote.projectName || '');
    }
  };

  const validateForm = (): boolean => {
    if (!customerId) {
      setError('Selecteer een klant');
      return false;
    }

    if (activeTab === 'quote' && !selectedQuoteId) {
      setError('Selecteer een goedgekeurde offerte');
      return false;
    }

    if (activeTab === 'manual' && manualItems.length === 0) {
      setError('Voeg minimaal één item toe');
      return false;
    }

    if (activeTab === 'manual') {
      for (const item of manualItems) {
        if (!item.description.trim()) {
          setError('Vul voor alle items een beschrijving in');
          return false;
        }
        if (item.quantity <= 0 || item.unitPrice <= 0) {
          setError('Aantal en prijs moeten groter zijn dan 0');
          return false;
        }
      }
    }

    return true;
  };

  const handleGenerateInvoice = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const request: AIInvoiceRequest = {
        customerId,
        projectName,
        invoiceDate,
        dueDate,
        vatPercentage
      };

      if (activeTab === 'quote') {
        request.quoteId = selectedQuoteId;
      } else {
        request.manualItems = manualItems.map(item => ({
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice
        }));
      }

      const response = await invoiceApi.createAIInvoice(request);

      if (response.success && response.data) {
        setSuccess('Factuur succesvol gegenereerd!');
        onInvoiceCreated(response.data.invoice);
        
        // Reset form
        setSelectedQuoteId('');
        setCustomerId('');
        setProjectName('');
        setManualItems([]);
        
        // Close modal after delay
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError(response.message || 'Fout bij het genereren van factuur');
      }
    } catch (error) {
      console.error('Error generating invoice:', error);
      setError('Fout bij het genereren van factuur');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-slate-900 border border-white/20 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/20">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">AI Factuur Wizard</h2>
              <p className="text-gray-400 text-sm">Genereer automatisch een professionele factuur</p>
            </div>
          </div>
          <Button
            variant="ghost"
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <X size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Tabs */}
          <div className="flex space-x-1 bg-slate-800/50 rounded-lg p-1">
            <button
              className={cn(
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                activeTab === 'quote'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white'
              )}
              onClick={() => setActiveTab('quote')}
            >
              <FileText className="inline-block mr-2 h-4 w-4" />
              Van bestaande Quote
            </button>
            <button
              className={cn(
                'flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors',
                activeTab === 'manual'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-400 hover:text-white'
              )}
              onClick={() => setActiveTab('manual')}
            >
              <Plus className="inline-block mr-2 h-4 w-4" />
              Handmatig
            </button>
          </div>

          {/* Error/Success Messages */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <AlertCircle size={16} className="text-red-400" />
                <p className="text-red-300">{error}</p>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Check size={16} className="text-green-400" />
                <p className="text-green-300">{success}</p>
              </div>
            </div>
          )}

          {/* Form Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Customer Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Klant *
              </label>
              <Select
                value={customerId}
                onChange={(value) => setCustomerId(Array.isArray(value) ? value[0] : value)}
                options={customers.map(customer => ({
                  value: customer.id,
                  label: customer.name
                }))}
                placeholder="Selecteer klant"
                aria-label="Selecteer klant"
              />
            </div>

            {/* Project Name */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Project Naam
              </label>
              <input
                type="text"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="Project naam"
                className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Invoice Date */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Factuurdatum
              </label>
              <input
                type="date"
                value={invoiceDate}
                onChange={(e) => setInvoiceDate(e.target.value)}
                className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Due Date */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Vervaldatum
              </label>
              <input
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full px-3 py-2 bg-slate-800/50 border border-slate-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* VAT Percentage */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                BTW Percentage
              </label>
              <Select
                value={vatPercentage.toString()}
                onChange={(value) => setVatPercentage(Number(Array.isArray(value) ? value[0] : value))}
                options={[
                  { value: "0", label: "0%" },
                  { value: "9", label: "9%" },
                  { value: "21", label: "21%" }
                ]}
                placeholder="Selecteer BTW percentage"
                aria-label="Selecteer BTW percentage"
              />
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'quote' ? (
            /* Quote Selection */
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Goedgekeurde Quote *
              </label>
              <Select
                value={selectedQuoteId}
                onChange={(value) => handleQuoteChange(Array.isArray(value) ? value[0] : value)}
                options={approvedQuotes.map(quote => ({
                  value: quote.id,
                  label: `${quote.number} - ${quote.customerName} - ${quote.projectName} (€${quote.total})`
                }))}
                placeholder="Selecteer een goedgekeurde offerte"
                aria-label="Selecteer een goedgekeurde offerte"
              />
              
              {approvedQuotes.length === 0 && (
                <p className="text-gray-400 text-sm mt-2">
                  Geen goedgekeurde offertes beschikbaar
                </p>
              )}
            </div>
          ) : (
            /* Manual Items */
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-gray-300">
                  Factuur Items
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addManualItem}
                  className="text-blue-400 border-blue-400 hover:bg-blue-500/20"
                >
                  <Plus size={14} className="mr-1" />
                  Item Toevoegen
                </Button>
              </div>

              {manualItems.length === 0 ? (
                <div className="text-center py-8 text-gray-400">
                  <FileText size={48} className="mx-auto mb-4 opacity-50" />
                  <p>Nog geen items toegevoegd</p>
                  <p className="text-sm">Klik op "Item Toevoegen" om te beginnen</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {manualItems.map((item, index) => (
                    <div key={item.id} className="grid grid-cols-12 gap-2 items-center p-3 bg-slate-800/30 rounded-lg border border-slate-700">
                      {/* Description */}
                      <div className="col-span-5">
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) => updateManualItem(item.id, 'description', e.target.value)}
                          placeholder="Beschrijving"
                          className="w-full px-2 py-1 bg-transparent border border-slate-700 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                      </div>

                      {/* Quantity */}
                      <div className="col-span-2">
                        <input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateManualItem(item.id, 'quantity', parseFloat(e.target.value) || 0)}
                          placeholder="Aantal"
                          min="0"
                          step="0.01"
                          className="w-full px-2 py-1 bg-transparent border border-slate-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                      </div>

                      {/* Unit Price */}
                      <div className="col-span-2">
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => updateManualItem(item.id, 'unitPrice', parseFloat(e.target.value) || 0)}
                          placeholder="€ Prijs"
                          min="0"
                          step="0.01"
                          className="w-full px-2 py-1 bg-transparent border border-slate-700 rounded text-white focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                      </div>

                      {/* Total */}
                      <div className="col-span-2 text-right">
                        <span className="text-white font-medium text-sm">
                          €{(item.quantity * item.unitPrice).toFixed(2)}
                        </span>
                      </div>

                      {/* Remove Button */}
                      <div className="col-span-1 text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeManualItem(item.id)}
                          className="text-red-400 hover:text-red-300 p-1"
                        >
                          <Trash2 size={14} />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-white/20">
          <div className="text-sm text-gray-400">
            {activeTab === 'quote' 
              ? 'AI zal automatisch factuurgegevens uit de geselecteerde quote halen'
              : 'AI zal de beschrijvingen optimaliseren en professionele factuur genereren'
            }
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuleren
            </Button>
            
            <Button
              onClick={handleGenerateInvoice}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  AI Factuur Genereren...
                </>
              ) : (
                <>
                  <Sparkles size={16} className="mr-2" />
                  Generate Invoice
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}; 
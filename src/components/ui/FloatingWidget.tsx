import React from 'react';
import { Bot, ChevronUp } from 'lucide-react';
import { cn } from '@/utils/cn';

interface FloatingWidgetProps {
  isVisible: boolean;
  onClick: () => void;
  icon?: React.ReactNode;
  badge?: string | number;
  pulse?: boolean;
}

export const FloatingWidget: React.FC<FloatingWidgetProps> = ({
  isVisible,
  onClick,
  icon = <Bot className="w-6 h-6" />,
  badge,
  pulse = false
}) => {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <button
        onClick={onClick}
        className={cn(
          'bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-4 shadow-2xl text-white',
          'hover:scale-105 hover:shadow-3xl transition-all duration-300',
          'focus:outline-none focus:ring-4 focus:ring-blue-500/30',
          pulse && 'animate-pulse'
        )}
        title="AI Quote Generator openen"
      >
        {icon}
        
        {badge && (
          <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
            {badge}
          </div>
        )}
        
        <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 hover:opacity-100 transition-opacity duration-300" />
      </button>
    </div>
  );
};



import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, X, Search } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

export interface SelectProps {
  value: string | string[];
  onChange: (value: string | string[]) => void;
  options: SelectOption[];
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  'aria-label'?: string;
  multiSelect?: boolean;
  searchable?: boolean;
  clearable?: boolean;
  size?: 'sm' | 'md' | 'lg';
  error?: boolean;
  helperText?: string;
}

export const Select: React.FC<SelectProps> = ({ // SelectOption already exported in original content
  value,
  onChange,
  options,
  placeholder = 'Selecteer een optie',
  className,
  disabled = false,
  'aria-label': ariaLabel,
  multiSelect = false,
  searchable = false,
  clearable = false,
  size = 'md',
  error = false,
  helperText,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const selectRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);
  const selectId = React.useMemo(() => `select-${Math.random().toString(36).substr(2, 9)}`, []);

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setFocusedIndex(prev => 
            prev < filteredOptions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : filteredOptions.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (focusedIndex >= 0 && focusedIndex < filteredOptions.length) {
            handleOptionSelect(filteredOptions[focusedIndex]);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setSearchTerm('');
          setFocusedIndex(-1);
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, focusedIndex, filteredOptions]);

  const handleOptionSelect = (option: SelectOption) => {
    if (option.disabled) return;

    if (multiSelect) {
      const currentValues = Array.isArray(value) ? value : [];
      const newValues = currentValues.includes(option.value)
        ? currentValues.filter(v => v !== option.value)
        : [...currentValues, option.value];
      onChange(newValues);
    } else {
      onChange(option.value);
      setIsOpen(false);
      setSearchTerm('');
      setFocusedIndex(-1);
    }
  };

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange(multiSelect ? [] : '');
    setSearchTerm('');
  };

  const getDisplayValue = () => {
    if (multiSelect) {
      const selectedOptions = Array.isArray(value) 
        ? options.filter(option => value.includes(option.value))
        : [];
      
      if (selectedOptions.length === 0) return placeholder;
      if (selectedOptions.length === 1) return selectedOptions[0].label;
      return `${selectedOptions.length} geselecteerd`;
    } else {
      const selectedOption = options.find(option => option.value === value);
      return selectedOption?.label || placeholder;
    }
  };

  const isOptionSelected = (optionValue: string) => {
    if (multiSelect) {
      return Array.isArray(value) && value.includes(optionValue);
    }
    return value === optionValue;
  };

  const sizeClasses = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-base',
    lg: 'px-4 py-3 text-lg',
  };

  return (
    <div className="relative" ref={selectRef}>
      <div
        className={cn(
          'relative bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg text-white transition-all duration-200 cursor-pointer',
          'hover:border-white/30 focus-within:border-primary-500 focus-within:ring-2 focus-within:ring-primary-500/20',
          disabled && 'opacity-50 cursor-not-allowed',
          error && 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500/20',
          sizeClasses[size],
          className
        )}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        role="combobox"
        aria-expanded={isOpen ? 'true' : 'false'}
        aria-haspopup="listbox"
        aria-controls={selectId}
        aria-label={ariaLabel || placeholder}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <span className={cn(
              'block truncate',
              (!value || (Array.isArray(value) && value.length === 0)) && 'text-white/60'
            )}>
              {getDisplayValue()}
            </span>
          </div>
          
          <div className="flex items-center gap-1 ml-2">
            {clearable && value && (
              <button
                type="button"
                onClick={handleClear}
                className="p-1 hover:bg-white/10 rounded transition-colors"
                aria-label="Clear selection"
              >
                <X size={16} />
              </button>
            )}
            <ChevronDown 
              size={16} 
              className={cn(
                'transition-transform duration-200',
                isOpen && 'rotate-180'
              )} 
            />
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-glass-dark backdrop-blur-sm border border-white/20 rounded-lg shadow-xl max-h-60 overflow-hidden">
          {searchable && (
            <div className="p-2 border-b border-white/10">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60" />
                <input
                  ref={searchRef}
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Zoeken..."
                  className="w-full pl-10 pr-3 py-2 bg-transparent border border-white/20 rounded-md text-white placeholder-white/60 focus:outline-none focus:border-primary-500"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          )}

          <div 
            id={selectId}
            className="max-h-48 overflow-y-auto"
            role="listbox"
          >
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-white/60 text-sm">
                Geen opties gevonden
              </div>
            ) : (
              filteredOptions.map((option, index) => (
                <div
                  key={option.value}
                  className={cn(
                    'px-3 py-2 cursor-pointer transition-colors flex items-center gap-2',
                    'hover:bg-white/10',
                    focusedIndex === index && 'bg-white/10',
                    isOptionSelected(option.value) && 'bg-primary-500/20 text-primary-300',
                    option.disabled && 'opacity-50 cursor-not-allowed hover:bg-transparent'
                  )}
                  onClick={() => handleOptionSelect(option)}
                  role="option"
                  aria-selected={isOptionSelected(option.value)}
                >
                  {option.icon && <span className="flex-shrink-0">{option.icon}</span>}
                  <span className="flex-1 truncate">{option.label}</span>
                  {isOptionSelected(option.value) && (
                    <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0" />
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {helperText && (
        <p className={cn(
          'mt-1 text-sm',
          error ? 'text-red-400' : 'text-white/60'
        )}>
          {helperText}
        </p>
      )}
    </div>
  );
};
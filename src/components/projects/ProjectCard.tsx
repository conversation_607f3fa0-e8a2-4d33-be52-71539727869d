import React from 'react';
import { Building, Calendar, User, DollarSign } from 'lucide-react';

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    client: string;
    status: string;
    budget: string;
    deadline: string;
  };
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {
  return (
    <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{project.name}</h3>
        <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">
          {project.status}
        </span>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="flex items-center text-gray-300">
          <User className="w-4 h-4 mr-2" />
          {project.client}
        </div>
        <div className="flex items-center text-gray-300">
          <DollarSign className="w-4 h-4 mr-2" />
          {project.budget}
        </div>
        <div className="flex items-center text-gray-300">
          <Calendar className="w-4 h-4 mr-2" />
          {project.deadline}
        </div>
      </div>
    </div>
  );
};
import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/Button';
import { FormInput } from '@/components/ui/Form';

interface ProjectFormData {
  name: string;
  client: string;
  budget: string;
  deadline: string;
  description: string;
}

export const ProjectForm: React.FC = () => {
  const { register, handleSubmit, formState: { errors } } = useForm<ProjectFormData>();

  const onSubmit = (data: ProjectFormData) => {
    console.log('Project data:', data);
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <FormInput
        label="Project Naam"
        {...register('name', { required: 'Project naam is verplicht' })}
        error={errors.name?.message}
      />
      
      <FormInput
        label="Klant"
        {...register('client', { required: 'Klant is verplicht' })}
        error={errors.client?.message}
      />
      
      <FormInput
        label="Budget"
        type="number"
        {...register('budget', { required: 'Budget is verplicht' })}
        error={errors.budget?.message}
      />
      
      <FormInput
        label="Deadline"
        type="date"
        {...register('deadline', { required: 'Deadline is verplicht' })}
        error={errors.deadline?.message}
      />
      
      <div className="flex justify-end">
        <Button type="submit">
          Project Opslaan
        </Button>
      </div>
    </form>
  );
};
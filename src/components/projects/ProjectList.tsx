import React from 'react';
import { Building, Calendar, User, DollarSign } from 'lucide-react';

export const ProjectList: React.FC = () => {
  const projects = [
    {
      id: 1,
      name: 'Badkamer Renovatie',
      client: '<PERSON>',
      status: 'In Progress',
      budget: '€15,000',
      deadline: '2024-02-15'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-white">Projecten</h1>
      </div>
      
      <div className="grid gap-6">
        {projects.map((project) => (
          <div key={project.id} className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">{project.name}</h3>
              <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm">
                {project.status}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex items-center text-gray-300">
                <User className="w-4 h-4 mr-2" />
                {project.client}
              </div>
              <div className="flex items-center text-gray-300">
                <DollarSign className="w-4 h-4 mr-2" />
                {project.budget}
              </div>
              <div className="flex items-center text-gray-300">
                <Calendar className="w-4 h-4 mr-2" />
                {project.deadline}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 

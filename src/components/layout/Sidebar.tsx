import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  FileText, 
  Users, 
  Building, 
  Settings, 
  BarChart3,
  MessageSquare,
  Menu,
  X,
  Mail,
  Receipt,
  Bot
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { ChevronIcon } from '@/components/ui';
import { useAppStore } from '@/stores/useAppStore';

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'Offertes', href: '/quotes', icon: FileText },
  { name: 'Klanten', href: '/customers', icon: Users },
  { name: 'Projecten', href: '/projects', icon: Building },
  { name: 'Facturen', href: '/invoices', icon: Receipt },
  { name: 'Email', href: '/email', icon: Mail },
  { name: 'Innovars AI', href: '/innovars-ai', icon: MessageSquare },
  { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  { name: 'Instellingen', href: '/settings', icon: Settings },
  {
    name: 'AI Smart Quote',
    href: '/ai-smart-quote',
    icon: Bot,
    badge: 'AI'
  }
];

export const Sidebar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const { sidebarCollapsed, setSidebarCollapsed } = useAppStore();

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 rounded-md bg-glass-light backdrop-blur-sm border border-white/20 text-white hover:bg-glass-dark transition-colors"
        >
          {isOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-40 bg-glass-light backdrop-blur-md border-r border-white/20 transform transition-all duration-300 ease-in-out lg:translate-x-0',
          sidebarCollapsed ? 'w-16' : 'w-64',
          isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo and Toggle */}
          <div className="flex items-center justify-between h-16 px-4 border-b border-white/20">
            {!sidebarCollapsed && (
              <h1 className="text-xl font-bold text-white">Quote.AI+CRM</h1>
            )}
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className={cn(
                'p-1 rounded-md text-gray-300 hover:text-white hover:bg-glass-dark transition-colors',
                sidebarCollapsed ? 'mx-auto' : ''
              )}
              title={sidebarCollapsed ? 'Uitklappen' : 'Inklappen'}
            >
              <ChevronIcon isCollapsed={sidebarCollapsed} />
            </button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href || 
                             (item.href === '/innovars-ai' && location.pathname === '/rita'); // Backward compatibility
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-primary-600 text-white shadow-glow'
                      : 'text-gray-300 hover:text-white hover:bg-glass-dark',
                    sidebarCollapsed && 'justify-center'
                  )}
                  onClick={() => setIsOpen(false)}
                  title={sidebarCollapsed ? item.name : undefined}
                >
                  <item.icon className={cn("h-5 w-5", !sidebarCollapsed && "mr-3")} />
                  {!sidebarCollapsed && item.name}
                  {item.name === 'Innovars AI' && (
                    <div className="ml-auto w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  )}
                </Link>
              );
            })}
          </nav>

          {/* User info */}
          <div className="p-4 border-t border-white/20">
            {!sidebarCollapsed ? (
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">U</span>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-white">Gebruiker</p>
                  <p className="text-xs text-gray-400">Premium Plan</p>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">U</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};


import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from '@/components/layout/Layout';
import { Dashboard } from '@/pages/Dashboard';
import { Quotes } from '@/pages/Quotes';
import { AIQuoteWizard } from '@/pages/AIQuoteWizard';
import { Customers } from '@/pages/Customers';
import { InnovarsAI } from '@/pages/InnovarsAI';
import { Analytics } from '@/pages/Analytics';
import { Projects } from '@/pages/Projects';
import { Settings } from '@/pages/Settings';
import { Email } from '@/pages/Email';
import { Invoices } from '@/pages/Invoices';
import { ToastProvider } from '@/contexts/ToastContext';
import './styles/globals.css';
import AISmartQuote from './pages/AISmartQuote';

function App() {
  return (
    <ToastProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/quotes" element={<Quotes />} />
            <Route path="/quotes/new-ai" element={<AIQuoteWizard />} />
            <Route path="/customers" element={<Customers />} />
            <Route path="/projects" element={<Projects />} />
            <Route path="/innovars-ai" element={<InnovarsAI />} />
            {/* Backward compatibility route */}
            <Route path="/rita" element={<InnovarsAI />} />
            <Route path="/analytics" element={<Analytics />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/email" element={<Email />} />
            <Route path="/invoices" element={<Invoices />} />
            <Route path="/ai-smart-quote" element={<AISmartQuote />} />
            {/* Add more routes here as we build them */}
          </Routes>
        </Layout>
      </Router>
    </ToastProvider>
  );
}

export default App;

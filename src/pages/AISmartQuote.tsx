import React, { useState } from 'react';
import { Camera, Send, Mail, MessageCircle, Bot, FileText, User, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { FormInput } from '@/components/ui/FormInput';
import { innovarsAIApi } from '@/api/innovars-ai';
import { quoteApi } from '@/api/quotes';
import { customerApi } from '@/api/customers';
import { projectApi } from '@/api/projects';
import { useToast } from '@/hooks/useToast';

interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
  address: string;
  company?: string;
}

interface ProjectPhoto {
  file: File;
  preview: string;
  id: string;
}

export const AISmartQuote: React.FC = () => {
  const [projectTitle, setProjectTitle] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    email: '',
    phone: '',
    address: '',
    company: ''
  });
  const [photos, setPhotos] = useState<ProjectPhoto[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedQuote, setGeneratedQuote] = useState<any>(null);
  const [sendMethod, setSendMethod] = useState<'email' | 'whatsapp'>('email');
  const { showToast } = useToast();

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (event) => {
        const newPhoto: ProjectPhoto = {
          file,
          preview: event.target?.result as string,
          id: Date.now().toString() + Math.random()
        };
        setPhotos(prev => [...prev, newPhoto]);
      };
      reader.readAsDataURL(file);
    });
  };

  const generateAIQuote = async () => {
    if (!projectTitle || !customerInfo.name || photos.length === 0) {
      showToast({
        type: 'error',
        title: 'Incomplete gegevens',
        message: 'Vul alle velden in en upload minimaal 1 foto.'
      });
      return;
    }

    setIsGenerating(true);

    try {
      // 1. Create customer first
      const customer = await customerApi.createCustomer({
        name: customerInfo.name,
        email: customerInfo.email,
        phone: customerInfo.phone,
        address: customerInfo.address,
        company: customerInfo.company
      });

      // 2. Convert photos to base64
      const photoData = await Promise.all(
        photos.map(async (photo) => {
          return new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string);
            reader.readAsDataURL(photo.file);
          });
        })
      );

      // 3. Generate AI quote with Innovars AI
      const aiQuoteRequest = {
        projectTitle,
        customerInfo,
        photos: photoData,
        context: 'full_project_analysis'
      };

      const aiResponse = await innovarsAIApi.generateSmartQuote(aiQuoteRequest);
      
      // 4. Create quote in database
      const quote = await quoteApi.createQuote({
        customerId: customer.data.id,
        customerName: customerInfo.name,
        projectName: projectTitle,
        items: aiResponse.data.items,
        subtotal: aiResponse.data.subtotal,
        vat: aiResponse.data.vat,
        total: aiResponse.data.total,
        description: aiResponse.data.description,
        aiGenerated: true,
        photos: photoData
      });

      // 5. Create linked project
      const project = await projectApi.createProject({
        name: projectTitle,
        customerId: customer.data.id,
        quoteId: quote.data.id,
        status: 'quote_sent',
        description: aiResponse.data.description,
        estimatedValue: aiResponse.data.total,
        photos: photoData
      });

      setGeneratedQuote({
        ...aiResponse.data,
        quoteId: quote.data.id,
        projectId: project.data.id,
        customerId: customer.data.id
      });

      showToast({
        type: 'success',
        title: 'AI Quote Gegenereerd!',
        message: 'Offerte, klant en project zijn aangemaakt.'
      });

    } catch (error) {
      showToast({
        type: 'error',
        title: 'Generatie Mislukt',
        message: 'Er is een fout opgetreden bij het genereren van de offerte.'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const sendQuote = async () => {
    if (!generatedQuote) return;

    try {
      if (sendMethod === 'whatsapp') {
        await innovarsAIApi.sendWhatsAppQuote({
          phoneNumber: customerInfo.phone,
          quoteId: generatedQuote.quoteId,
          customerName: customerInfo.name,
          projectTitle,
          total: generatedQuote.total
        });
      } else {
        await innovarsAIApi.sendEmailQuote({
          email: customerInfo.email,
          quoteId: generatedQuote.quoteId,
          customerName: customerInfo.name,
          projectTitle,
          total: generatedQuote.total
        });
      }

      showToast({
        type: 'success',
        title: 'Offerte Verzonden!',
        message: `Offerte is verzonden via ${sendMethod === 'whatsapp' ? 'WhatsApp' : 'email'}.`
      });

    } catch (error) {
      showToast({
        type: 'error',
        title: 'Verzending Mislukt',
        message: 'Er is een fout opgetreden bij het verzenden.'
      });
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2">
          <Bot className="w-8 h-8 text-blue-400" />
          <h1 className="text-3xl font-bold text-white">AI Smart Quote Generator</h1>
        </div>
        <p className="text-gray-300">
          Upload foto's, voer klantgegevens in en laat AI automatisch een professionele offerte genereren
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          {/* Project Info */}
          <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Project Informatie
            </h2>
            <FormInput
              label="Project Titel"
              value={projectTitle}
              onChange={(e) => setProjectTitle(e.target.value)}
              placeholder="Bijv. Badkamer renovatie, Keuken verbouwing..."
            />
          </div>

          {/* Customer Info */}
          <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <User className="w-5 h-5 mr-2" />
              Klant Gegevens
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormInput
                label="Naam"
                value={customerInfo.name}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Voor- en achternaam"
              />
              <FormInput
                label="Bedrijf (optioneel)"
                value={customerInfo.company}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, company: e.target.value }))}
                placeholder="Bedrijfsnaam"
              />
              <FormInput
                label="Email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
              />
              <FormInput
                label="Telefoon"
                value={customerInfo.phone}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="+31 6 12345678"
              />
            </div>
            <div className="mt-4">
              <FormInput
                label="Adres"
                value={customerInfo.address}
                onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                placeholder="Straat, huisnummer, postcode, plaats"
                icon={MapPin}
              />
            </div>
          </div>

          {/* Photo Upload */}
          <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
              <Camera className="w-5 h-5 mr-2" />
              Project Foto's
            </h2>
            <div className="space-y-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handlePhotoUpload}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="flex items-center justify-center w-full h-32 border-2 border-dashed border-white/30 rounded-lg cursor-pointer hover:border-white/50 transition-colors"
              >
                <div className="text-center">
                  <Camera className="w-8 h-8 text-white/60 mx-auto mb-2" />
                  <p className="text-white/60">Klik om foto's te uploaden</p>
                </div>
              </label>
              
              {photos.length > 0 && (
                <div className="grid grid-cols-2 gap-4">
                  {photos.map((photo) => (
                    <div key={photo.id} className="relative">
                      <img
                        src={photo.preview}
                        alt="Project foto"
                        className="w-full h-24 object-cover rounded-lg"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <Button
            onClick={generateAIQuote}
            disabled={isGenerating}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Bot className="w-5 h-5 mr-2 animate-spin" />
                AI Genereert Offerte...
              </>
            ) : (
              <>
                <Bot className="w-5 h-5 mr-2" />
                Genereer AI Offerte
              </>
            )}
          </Button>
        </div>

        {/* Preview Section */}
        <div className="space-y-6">
          {generatedQuote ? (
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
              <h2 className="text-xl font-semibold text-white mb-4">Gegenereerde Offerte</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-white">{generatedQuote.title}</h3>
                  <p className="text-gray-300 text-sm mt-1">{generatedQuote.description}</p>
                </div>

                <div className="space-y-2">
                  {generatedQuote.items.map((item: any, index: number) => (
                    <div key={index} className="flex justify-between py-2 border-b border-white/10">
                      <div>
                        <p className="text-white">{item.description}</p>
                        <p className="text-gray-400 text-sm">{item.quantity}x €{item.unitPrice}</p>
                      </div>
                      <p className="text-white font-semibold">€{item.total.toFixed(2)}</p>
                    </div>
                  ))}
                </div>

                <div className="border-t border-white/20 pt-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Subtotaal:</span>
                    <span className="text-white">€{generatedQuote.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">BTW (21%):</span>
                    <span className="text-white">€{generatedQuote.vat.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold">
                    <span className="text-white">Totaal:</span>
                    <span className="text-green-400">€{generatedQuote.total.toFixed(2)}</span>
                  </div>
                </div>

                {/* Send Options */}
                <div className="space-y-4 pt-4 border-t border-white/20">
                  <div className="flex space-x-4">
                    <button
                      onClick={() => setSendMethod('email')}
                      className={`flex-1 p-3 rounded-lg border transition-colors ${
                        sendMethod === 'email'
                          ? 'bg-blue-600 border-blue-500 text-white'
                          : 'bg-white/10 border-white/20 text-gray-300'
                      }`}
                    >
                      <Mail className="w-4 h-4 mx-auto mb-1" />
                      Email
                    </button>
                    <button
                      onClick={() => setSendMethod('whatsapp')}
                      className={`flex-1 p-3 rounded-lg border transition-colors ${
                        sendMethod === 'whatsapp'
                          ? 'bg-green-600 border-green-500 text-white'
                          : 'bg-white/10 border-white/20 text-gray-300'
                      }`}
                    >
                      <MessageCircle className="w-4 h-4 mx-auto mb-1" />
                      WhatsApp
                    </button>
                  </div>

                  <Button
                    onClick={sendQuote}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600"
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Verstuur via {sendMethod === 'whatsapp' ? 'WhatsApp' : 'Email'}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20 text-center">
              <Bot className="w-16 h-16 text-white/40 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Klaar voor AI Generatie</h3>
              <p className="text-gray-300">
                Vul de gegevens in en upload foto's om een AI-gegenereerde offerte te maken
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AISmartQuote;
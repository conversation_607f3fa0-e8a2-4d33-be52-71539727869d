import React from 'react';
import { InvoiceList } from '@/components/invoices/InvoiceList';
import { Invoice } from '@/types/invoice';

export const Invoices: React.FC = () => {
  const handleInvoiceClick = (invoice: Invoice) => {
    console.log('Invoice clicked:', invoice);
    // TODO: Open invoice detail view
  };

  const handleEditInvoice = (invoice: Invoice) => {
    console.log('Edit invoice:', invoice);
    // TODO: Open invoice edit form
  };

  const handleDeleteInvoice = (invoiceId: string) => {
    console.log('Delete invoice:', invoiceId);
    // TODO: Handle invoice deletion
  };

  return (
    <div className="space-y-6">
      <InvoiceList
        onInvoiceClick={handleInvoiceClick}
        onEditInvoice={handleEditInvoice}
        onDeleteInvoice={handleDeleteInvoice}
      />
    </div>
  );
};

export default Invoices;

import React from 'react';
import { MessageSquare, Mic, FileText, Zap } from 'lucide-react';
import { Button } from '@/components/ui/Button';

export const InnovarsAI: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-white">Innovars AI Assistant</h1>
        <p className="text-gray-300 max-w-2xl mx-auto">
          Gebruik AI om automatisch offertes te genereren, klantgesprekken te analyseren, 
          en slimme inzichten te krijgen voor je bedrijf.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
          <MessageSquare className="w-8 h-8 text-blue-400 mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Chat Assistant</h3>
          <p className="text-gray-300 text-sm mb-4">
            Stel vragen over je projecten en krijg directe antwoorden.
          </p>
          <Button variant="outline" className="w-full">
            Start Chat
          </Button>
        </div>

        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
          <Mic className="w-8 h-8 text-green-400 mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Voice Commands</h3>
          <p className="text-gray-300 text-sm mb-4">
            Gebruik spraakcommando's om snel offertes te maken.
          </p>
          <Button variant="outline" className="w-full">
            Start Recording
          </Button>
        </div>

        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
          <FileText className="w-8 h-8 text-purple-400 mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Smart Quotes</h3>
          <p className="text-gray-300 text-sm mb-4">
            Genereer automatisch offertes op basis van foto's.
          </p>
          <Button variant="outline" className="w-full">
            Nieuwe Offerte
          </Button>
        </div>

        <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
          <Zap className="w-8 h-8 text-yellow-400 mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">AI Insights</h3>
          <p className="text-gray-300 text-sm mb-4">
            Krijg slimme inzichten over je bedrijfsprestaties.
          </p>
          <Button variant="outline" className="w-full">
            Bekijk Insights
          </Button>
        </div>
      </div>

      <div className="bg-white/10 backdrop-blur-md rounded-lg p-6 border border-white/20">
        <h2 className="text-xl font-semibold text-white mb-4">Recente AI Activiteit</h2>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2">
            <span className="text-gray-300">AI Quote gegenereerd voor "Badkamer Project"</span>
            <span className="text-sm text-gray-400">2 min geleden</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <span className="text-gray-300">Voice command verwerkt: "Nieuwe klant toevoegen"</span>
            <span className="text-sm text-gray-400">15 min geleden</span>
          </div>
          <div className="flex items-center justify-between py-2">
            <span className="text-gray-300">AI Insight: "Verhoog prijzen met 8% voor betere marge"</span>
            <span className="text-sm text-gray-400">1 uur geleden</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InnovarsAI;

import { api, withRetry } from './client';
import { Quote, QuoteItem } from '@/stores/useAppStore';

// Quote API types
export interface CreateQuoteRequest {
  customerId: string;
  customerName: string;
  projectName: string;
  items: Omit<QuoteItem, 'id'>[];
  expiresAt: Date;
}

export interface UpdateQuoteRequest {
  customerId?: string;
  customerName?: string;
  projectName?: string;
  status?: Quote['status'];
  items?: Omit<QuoteItem, 'id'>[];
  expiresAt?: Date;
}

export interface QuoteFilters {
  status?: Quote['status'];
  customerId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

// Mock data voor development
const mockQuotes: Quote[] = [
  {
    id: '1',
    number: 'Q-2024-001',
    customerId: 'cust-1',
    customerName: '<PERSON> V<PERSON>ulen',
    projectName: 'Bathroom Renovation',
    status: 'sent',
    items: [
      {
        id: 'item-1',
        description: 'Bathroom tiles (20m²)',
        quantity: 20,
        unitPrice: 45,
        total: 900
      },
      {
        id: 'item-2',
        description: 'Plumbing work',
        quantity: 1,
        unitPrice: 350,
        total: 350
      }
    ],
    amount: 1250,
    createdAt: new Date('2024-01-15'),
    expiresAt: new Date('2024-02-15')
  },
  {
    id: '2',
    number: 'Q-2024-002',
    customerId: 'cust-2',
    customerName: 'Marie Dubois',
    projectName: 'Kitchen Extension',
    status: 'draft',
    items: [
      {
        id: 'item-3',
        description: 'Kitchen cabinets',
        quantity: 1,
        unitPrice: 2500,
        total: 2500
      },
      {
        id: 'item-4',
        description: 'Electrical work',
        quantity: 1,
        unitPrice: 800,
        total: 800
      }
    ],
    amount: 3300,
    createdAt: new Date('2024-01-20'),
    expiresAt: new Date('2024-02-20')
  },
  {
    id: '3',
    number: 'Q-2024-003',
    customerId: 'cust-3',
    customerName: 'Piet Janssen',
    projectName: 'Roof Repair',
    status: 'accepted',
    items: [
      {
        id: 'item-5',
        description: 'Roof tiles replacement',
        quantity: 50,
        unitPrice: 12,
        total: 600
      },
      {
        id: 'item-6',
        description: 'Gutter installation',
        quantity: 1,
        unitPrice: 400,
        total: 400
      }
    ],
    amount: 1000,
    createdAt: new Date('2024-01-10'),
    expiresAt: new Date('2024-02-10')
  }
];

// Quote API services
export const quoteApi = {
  // Gebruik de nieuwe fetchWithToast API voor betere error handling:
  // 
  // Oude manier (axios):
  // const quotes = await api.get<Quote[]>('/quotes')
  //
  // Nieuwe manier (fetchWithToast):
  // const quotes = await fetchApi.get<Quote[]>('/quotes', {
  //   toastMessage: 'Offertes succesvol geladen!',
  //   errorMessage: 'Fout bij het laden van offertes'
  // })
  //
  // Voordelen van fetchWithToast:
  // - Automatische toast notifications bij errors
  // - Console logging voor debugging
  // - Betere error messages voor gebruikers
  // - Consistent error handling in hele app
  // Get all quotes with optional filters
  getQuotes: async (filters?: QuoteFilters) => {
    // Mock implementation voor development
    return new Promise<{ data: Quote[] }>((resolve) => {
      setTimeout(() => {
        let filteredQuotes = [...mockQuotes];
        
        // Apply filters
        if (filters?.status) {
          filteredQuotes = filteredQuotes.filter(q => q.status === filters.status);
        }
        
        if (filters?.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredQuotes = filteredQuotes.filter(q => 
            q.projectName.toLowerCase().includes(searchTerm) ||
            q.customerName.toLowerCase().includes(searchTerm) ||
            q.number.toLowerCase().includes(searchTerm)
          );
        }
        
        resolve({ data: filteredQuotes });
      }, 500); // Simulate network delay
    });
  },

  // Get quote by ID
  getQuote: async (id: string) => {
    const quote = mockQuotes.find(q => q.id === id);
    if (!quote) {
      throw new Error('Quote not found');
    }
    return { data: quote };
  },

  // Create new quote
  createQuote: async (data: CreateQuoteRequest) => {
    const newQuote: Quote = {
      id: `Q-${Date.now()}`,
      number: `Q-2024-${mockQuotes.length + 1}`,
      customerId: data.customerId,
      customerName: data.customerName,
      projectName: data.projectName,
      status: 'draft',
      items: data.items.map((item, index) => ({
        ...item,
        id: `item-${Date.now()}-${index}`,
        total: item.quantity * item.unitPrice
      })),
      amount: data.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0),
      expiresAt: data.expiresAt,
      createdAt: new Date()
    };
    
    mockQuotes.push(newQuote);
    return { data: newQuote };
  },

  // Update quote
  updateQuote: async (id: string, data: UpdateQuoteRequest) => {
    const quoteIndex = mockQuotes.findIndex(q => q.id === id);
    if (quoteIndex === -1) {
      throw new Error('Quote not found');
    }
    
    const updatedQuote: Quote = {
      ...mockQuotes[quoteIndex],
      ...data,
      items: data.items?.map(item => ({
        ...item,
        id: item.id || `item-${Date.now()}-${Math.random()}`
      })) || mockQuotes[quoteIndex].items,
      updatedAt: new Date(),
    };
    
    mockQuotes[quoteIndex] = updatedQuote;
    
    return { data: updatedQuote };
  },

  // Delete quote
  deleteQuote: async (id: string) => {
    const quoteIndex = mockQuotes.findIndex(q => q.id === id);
    if (quoteIndex === -1) {
      throw new Error('Quote not found');
    }
    
    mockQuotes.splice(quoteIndex, 1);
    return { success: true };
  },

  // Update quote status
  updateQuoteStatus: async (id: string, status: Quote['status']) => {
    const quote = mockQuotes.find(q => q.id === id);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    quote.status = status;
    return { data: quote };
  },

  // Add item to quote
  addQuoteItem: async (quoteId: string, item: Omit<QuoteItem, 'id'>) => {
    const quote = mockQuotes.find(q => q.id === quoteId);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    const newItem: QuoteItem = {
      ...item,
      id: `item-${Date.now()}`,
      total: item.quantity * item.unitPrice
    };
    
    quote.items.push(newItem);
    quote.amount = quote.items.reduce((sum, item) => sum + item.total, 0);
    
    return { data: newItem };
  },

  // Update quote item
  updateQuoteItem: async (quoteId: string, itemId: string, item: Partial<Omit<QuoteItem, 'id'>>) => {
    const quote = mockQuotes.find(q => q.id === quoteId);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    const itemIndex = quote.items.findIndex(i => i.id === itemId);
    if (itemIndex === -1) {
      throw new Error('Item not found');
    }
    
    const updatedItem = { ...quote.items[itemIndex], ...item };
    if (updatedItem.quantity && updatedItem.unitPrice) {
      updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
    }
    
    quote.items[itemIndex] = updatedItem;
    quote.amount = quote.items.reduce((sum, item) => sum + item.total, 0);
    
    return { data: updatedItem };
  },

  // Remove item from quote
  removeQuoteItem: async (quoteId: string, itemId: string) => {
    const quote = mockQuotes.find(q => q.id === quoteId);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    const itemIndex = quote.items.findIndex(i => i.id === itemId);
    if (itemIndex === -1) {
      throw new Error('Item not found');
    }
    
    quote.items.splice(itemIndex, 1);
    quote.amount = quote.items.reduce((sum, item) => sum + item.total, 0);
    
    return { success: true };
  },

  // Generate PDF for quote
  generatePdf: async (id: string) => {
    const quote = mockQuotes.find(q => q.id === id);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    // Mock PDF generation
    return { data: new Blob(['Mock PDF content'], { type: 'application/pdf' }) };
  },

  // Send quote via email
  sendQuote: async (id: string, email: string, message?: string) => {
    const quote = mockQuotes.find(q => q.id === id);
    if (!quote) {
      throw new Error('Quote not found');
    }
    
    // Mock email sending
    console.log('Sending quote to:', email, 'Message:', message);
    return { success: true };
  },

  // Duplicate quote
  duplicateQuote: async (id: string, newProjectName?: string) => {
    const originalQuote = mockQuotes.find(q => q.id === id);
    if (!originalQuote) {
      throw new Error('Quote not found');
    }
    
    const duplicatedQuote: Quote = {
      ...originalQuote,
      id: `Q-${Date.now()}`,
      number: `Q-2024-${mockQuotes.length + 1}`,
      projectName: newProjectName || `${originalQuote.projectName} (Copy)`,
      status: 'draft',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    };
    
    mockQuotes.push(duplicatedQuote);
    return { data: duplicatedQuote };
  },

  // Get quote statistics
  getQuoteStats: async () => {
    const stats = {
      total: mockQuotes.length,
      draft: mockQuotes.filter(q => q.status === 'draft').length,
      sent: mockQuotes.filter(q => q.status === 'sent').length,
      accepted: mockQuotes.filter(q => q.status === 'accepted').length,
      rejected: mockQuotes.filter(q => q.status === 'rejected').length,
      expired: mockQuotes.filter(q => q.status === 'expired').length,
      totalValue: mockQuotes.reduce((sum, q) => sum + q.amount, 0)
    };
    
    return { data: stats };
  },

  // Get quotes by customer
  getQuotesByCustomer: async (customerId: string) => {
    const customerQuotes = mockQuotes.filter(q => q.customerId === customerId);
    return { data: customerQuotes };
  },
}; 

# Deployment Checklist

## Pre-deployment
- [ ] Alle console errors opgelost
- [ ] Build succesvol: `npm run build`
- [ ] Type checking: `npm run type-check`
- [ ] Linting: `npm run lint`
- [ ] Tests: `npm run test`

## Environment Setup
- [ ] Environment variables geconfigureerd
- [ ] API endpoints correct ingesteld
- [ ] CORS instellingen correct

## Performance
- [ ] Bundle size geoptimaliseerd
- [ ] Images geoptimaliseerd
- [ ] Lazy loading geïmplementeerd